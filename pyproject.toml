[tool.poetry]
name = "fitness-coach"
version = "0.1.0"
description = "Multi-agent fitness and nutrition coaching system built with Google's Agent Development Kit"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "fitness_coach"}]

[tool.poetry.dependencies]
python = "^3.10"
google-adk = "^1.1.1"
google-generativeai = "^0.8.5"
python-dotenv = "^1.0.0"
pydantic = "^2.5.0"
asyncpg = "^0.30.0"
nest_asyncio = "*"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
black = "^23.7.0"
isort = "^5.12.0"
mypy = "^1.5.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ["py310"]
exclude = '''
/(
    \.git
  | \.venv
  | env
  | venv
  | __pycache__
)/
'''

[tool.isort]
profile = "black"
line_length = 100

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true 